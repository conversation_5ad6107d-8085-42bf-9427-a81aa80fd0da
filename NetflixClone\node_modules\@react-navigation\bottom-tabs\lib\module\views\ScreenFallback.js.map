{"version": 3, "names": ["React", "View", "jsx", "_jsx", "Screens", "require", "e", "MaybeScreenContainer", "enabled", "rest", "screensEnabled", "ScreenContainer", "MaybeScreen", "active", "Screen", "activityState"], "sourceRoot": "../../../src", "sources": ["views/ScreenFallback.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAGEC,IAAI,QAGC,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAWtB,IAAIC,OAA0D;AAE9D,IAAI;EACFA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC3C,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;AAAA;AAGF,OAAO,MAAMC,oBAAoB,GAAGA,CAAC;EACnCC,OAAO;EACP,GAAGC;AAKL,CAAC,KAAK;EACJ,IAAIL,OAAO,EAAEM,cAAc,GAAG,CAAC,EAAE;IAC/B,oBAAOP,IAAA,CAACC,OAAO,CAACO,eAAe;MAACH,OAAO,EAAEA,OAAQ;MAAA,GAAKC;IAAI,CAAG,CAAC;EAChE;EAEA,oBAAON,IAAA,CAACF,IAAI;IAAA,GAAKQ;EAAI,CAAG,CAAC;AAC3B,CAAC;AAED,OAAO,SAASG,WAAWA,CAAC;EAAEJ,OAAO;EAAEK,MAAM;EAAE,GAAGJ;AAAwB,CAAC,EAAE;EAC3E,IAAIL,OAAO,EAAEM,cAAc,GAAG,CAAC,EAAE;IAC/B,oBACEP,IAAA,CAACC,OAAO,CAACU,MAAM;MAACN,OAAO,EAAEA,OAAQ;MAACO,aAAa,EAAEF,MAAO;MAAA,GAAKJ;IAAI,CAAG,CAAC;EAEzE;EAEA,oBAAON,IAAA,CAACF,IAAI;IAAA,GAAKQ;EAAI,CAAG,CAAC;AAC3B", "ignoreList": []}