{"version": 3, "file": "BlurView.types.js", "sourceRoot": "", "sources": ["../src/BlurView.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ViewProps } from 'react-native';\n\n/**\n * Blur method to use on Android.\n *\n * - `'none'` - Falls back to a semi-transparent view instead of rendering a blur effect.\n *\n * - `'dimezisBlurView'` - Uses a native blur view implementation based on [BlurView](https://github.com/Dimezis/BlurView) library. This method may lead to decreased performance and rendering issues during transitions made by `react-native-screens`.\n *\n * @platform android\n */\nexport type ExperimentalBlurMethod = 'none' | 'dimezisBlurView';\n\nexport type BlurViewProps = {\n  /**\n   * A tint mode which will be applied to the view.\n   * @default 'default'\n   */\n  tint?: BlurTint;\n  /**\n   * A number from `1` to `100` to control the intensity of the blur effect.\n   *\n   * You can animate this property using `react-native-reanimated`.\n   *\n   * @default 50\n   */\n  intensity?: number;\n  /**\n   * A number by which the blur intensity will be divided on Android.\n   *\n   * When using experimental blur methods on Android, the perceived blur intensity might differ from iOS\n   * at different intensity levels. This property can be used to fine tune it on Android to match it\n   * more closely with iOS.\n   * @default 4\n   * @platform android\n   *\n   */\n  blurReductionFactor?: number;\n\n  /**\n   * Blur method to use on Android.\n   *\n   * > **warning** Currently, `BlurView` support is experimental on Android and may cause performance and graphical issues.\n   * It can be enabled by setting this property.\n   *\n   * @default 'none'\n   * @platform android\n   */\n  experimentalBlurMethod?: ExperimentalBlurMethod;\n} & ViewProps;\n\nexport type BlurTint =\n  | 'light'\n  | 'dark'\n  | 'default'\n  | 'extraLight'\n  | 'regular'\n  | 'prominent'\n  | 'systemUltraThinMaterial'\n  | 'systemThinMaterial'\n  | 'systemMaterial'\n  | 'systemThickMaterial'\n  | 'systemChromeMaterial'\n  | 'systemUltraThinMaterialLight'\n  | 'systemThinMaterialLight'\n  | 'systemMaterialLight'\n  | 'systemThickMaterialLight'\n  | 'systemChromeMaterialLight'\n  | 'systemUltraThinMaterialDark'\n  | 'systemThinMaterialDark'\n  | 'systemMaterialDark'\n  | 'systemThickMaterialDark'\n  | 'systemChromeMaterialDark';\n"]}