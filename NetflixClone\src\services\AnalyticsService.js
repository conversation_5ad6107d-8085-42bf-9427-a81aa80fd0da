import AsyncStorage from '@react-native-async-storage/async-storage';

const ANALYTICS_KEY = '@analytics_data';
const USER_PREFERENCES_KEY = '@user_preferences';

// Analytics Event Types
export const ANALYTICS_EVENTS = {
  CONTENT_VIEW: 'content_view',
  CONTENT_PLAY: 'content_play',
  CONTENT_PAUSE: 'content_pause',
  CONTENT_COMPLETE: 'content_complete',
  CONTENT_SKIP: 'content_skip',
  CONTENT_RATE: 'content_rate',
  SEARCH_QUERY: 'search_query',
  MY_LIST_ADD: 'my_list_add',
  MY_LIST_REMOVE: 'my_list_remove',
  GENRE_INTERACTION: 'genre_interaction',
  TIME_SPENT: 'time_spent',
  NAVIGATION: 'navigation'
};

class AnalyticsService {
  constructor() {
    this.analyticsData = [];
    this.userPreferences = {};
    this.sessionStartTime = Date.now();
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      const [storedAnalytics, storedPreferences] = await Promise.all([
        AsyncStorage.getItem(ANALYTICS_KEY),
        AsyncStorage.getItem(USER_PREFERENCES_KEY)
      ]);
      
      if (storedAnalytics) {
        this.analyticsData = JSON.parse(storedAnalytics);
      }
      
      if (storedPreferences) {
        this.userPreferences = JSON.parse(storedPreferences);
      }
      
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing analytics:', error);
      this.analyticsData = [];
      this.userPreferences = {};
      this.initialized = true;
    }
  }

  async trackEvent(eventType, data = {}) {
    await this.initialize();
    
    const event = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: eventType,
      timestamp: Date.now(),
      sessionId: this.sessionStartTime,
      data: {
        ...data,
        userAgent: 'mobile_app',
        platform: 'react_native'
      }
    };

    this.analyticsData.push(event);
    
    // Keep only last 1000 events to prevent storage bloat
    if (this.analyticsData.length > 1000) {
      this.analyticsData = this.analyticsData.slice(-1000);
    }
    
    await this.persistAnalytics();
    
    // Update user preferences based on the event
    await this.updateUserPreferences(event);
  }

  async trackContentView(item, mediaType, source = 'unknown') {
    await this.trackEvent(ANALYTICS_EVENTS.CONTENT_VIEW, {
      contentId: item.id,
      contentTitle: item.title || item.name,
      mediaType,
      genres: item.genre_ids || [],
      rating: item.vote_average,
      language: item.original_language,
      source,
      popularity: item.popularity
    });
  }

  async trackContentPlay(item, mediaType, watchProgress = null) {
    await this.trackEvent(ANALYTICS_EVENTS.CONTENT_PLAY, {
      contentId: item.id,
      contentTitle: item.title || item.name,
      mediaType,
      genres: item.genre_ids || [],
      resumePosition: watchProgress?.currentTime || 0,
      isResume: watchProgress?.progressPercent > 0.05,
      source: 'player'
    });
  }

  async trackContentComplete(item, mediaType, watchDuration, totalDuration) {
    const completionRate = totalDuration > 0 ? watchDuration / totalDuration : 0;
    
    await this.trackEvent(ANALYTICS_EVENTS.CONTENT_COMPLETE, {
      contentId: item.id,
      contentTitle: item.title || item.name,
      mediaType,
      genres: item.genre_ids || [],
      watchDuration,
      totalDuration,
      completionRate,
      timeOfDay: new Date().getHours()
    });
  }

  async trackSearchQuery(query, resultsCount = 0) {
    await this.trackEvent(ANALYTICS_EVENTS.SEARCH_QUERY, {
      query: query.toLowerCase(),
      queryLength: query.length,
      resultsCount,
      timeOfDay: new Date().getHours()
    });
  }

  async trackMyListAction(action, item, mediaType, category = null) {
    await this.trackEvent(action === 'add' ? ANALYTICS_EVENTS.MY_LIST_ADD : ANALYTICS_EVENTS.MY_LIST_REMOVE, {
      contentId: item.id,
      contentTitle: item.title || item.name,
      mediaType,
      category,
      genres: item.genre_ids || []
    });
  }

  async trackTimeSpent(screenName, duration) {
    await this.trackEvent(ANALYTICS_EVENTS.TIME_SPENT, {
      screenName,
      duration,
      timeOfDay: new Date().getHours()
    });
  }

  async trackNavigation(fromScreen, toScreen) {
    await this.trackEvent(ANALYTICS_EVENTS.NAVIGATION, {
      fromScreen,
      toScreen,
      timeOfDay: new Date().getHours()
    });
  }

  async updateUserPreferences(event) {
    const { type, data } = event;
    
    // Update genre preferences
    if (data.genres && Array.isArray(data.genres)) {
      if (!this.userPreferences.genrePreferences) {
        this.userPreferences.genrePreferences = {};
      }
      
      data.genres.forEach(genreId => {
        this.userPreferences.genrePreferences[genreId] = 
          (this.userPreferences.genrePreferences[genreId] || 0) + 1;
      });
    }

    // Update language preferences
    if (data.language) {
      if (!this.userPreferences.languagePreferences) {
        this.userPreferences.languagePreferences = {};
      }
      this.userPreferences.languagePreferences[data.language] = 
        (this.userPreferences.languagePreferences[data.language] || 0) + 1;
    }

    // Update viewing time patterns
    if (data.timeOfDay !== undefined) {
      if (!this.userPreferences.viewingTimePatterns) {
        this.userPreferences.viewingTimePatterns = {};
      }
      this.userPreferences.viewingTimePatterns[data.timeOfDay] = 
        (this.userPreferences.viewingTimePatterns[data.timeOfDay] || 0) + 1;
    }

    // Update media type preferences
    if (data.mediaType) {
      if (!this.userPreferences.mediaTypePreferences) {
        this.userPreferences.mediaTypePreferences = {};
      }
      this.userPreferences.mediaTypePreferences[data.mediaType] = 
        (this.userPreferences.mediaTypePreferences[data.mediaType] || 0) + 1;
    }

    // Update completion rate tracking
    if (type === ANALYTICS_EVENTS.CONTENT_COMPLETE && data.completionRate !== undefined) {
      if (!this.userPreferences.completionRates) {
        this.userPreferences.completionRates = [];
      }
      this.userPreferences.completionRates.push({
        contentId: data.contentId,
        mediaType: data.mediaType,
        completionRate: data.completionRate,
        timestamp: Date.now()
      });
      
      // Keep only last 100 completion rates
      if (this.userPreferences.completionRates.length > 100) {
        this.userPreferences.completionRates = this.userPreferences.completionRates.slice(-100);
      }
    }

    await this.persistPreferences();
  }

  async getAnalyticsData(eventType = null, limit = 100) {
    await this.initialize();
    
    let data = [...this.analyticsData];
    
    if (eventType) {
      data = data.filter(event => event.type === eventType);
    }
    
    return data
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  async getUserPreferences() {
    await this.initialize();
    return { ...this.userPreferences };
  }

  async getGenrePreferences() {
    await this.initialize();
    
    const preferences = this.userPreferences.genrePreferences || {};
    return Object.entries(preferences)
      .sort(([,a], [,b]) => b - a)
      .map(([genreId, count]) => ({ genreId: parseInt(genreId), count }));
  }

  async getViewingPatterns() {
    await this.initialize();
    
    const patterns = {
      timeOfDay: this.userPreferences.viewingTimePatterns || {},
      mediaTypes: this.userPreferences.mediaTypePreferences || {},
      languages: this.userPreferences.languagePreferences || {},
      averageCompletionRate: 0
    };

    // Calculate average completion rate
    const completionRates = this.userPreferences.completionRates || [];
    if (completionRates.length > 0) {
      const totalRate = completionRates.reduce((sum, item) => sum + item.completionRate, 0);
      patterns.averageCompletionRate = totalRate / completionRates.length;
    }

    return patterns;
  }

  async getRecommendationData() {
    await this.initialize();
    
    const recentViews = await this.getAnalyticsData(ANALYTICS_EVENTS.CONTENT_VIEW, 50);
    const recentPlays = await this.getAnalyticsData(ANALYTICS_EVENTS.CONTENT_PLAY, 30);
    const completions = await this.getAnalyticsData(ANALYTICS_EVENTS.CONTENT_COMPLETE, 20);
    
    return {
      recentViews,
      recentPlays,
      completions,
      preferences: await this.getUserPreferences(),
      patterns: await this.getViewingPatterns()
    };
  }

  async clearAnalytics() {
    this.analyticsData = [];
    this.userPreferences = {};
    await Promise.all([
      this.persistAnalytics(),
      this.persistPreferences()
    ]);
  }

  async persistAnalytics() {
    try {
      await AsyncStorage.setItem(ANALYTICS_KEY, JSON.stringify(this.analyticsData));
    } catch (error) {
      console.error('Error persisting analytics:', error);
    }
  }

  async persistPreferences() {
    try {
      await AsyncStorage.setItem(USER_PREFERENCES_KEY, JSON.stringify(this.userPreferences));
    } catch (error) {
      console.error('Error persisting preferences:', error);
    }
  }
}

export default new AnalyticsService();
