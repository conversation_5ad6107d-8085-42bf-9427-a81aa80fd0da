import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  ImageBackground,
  Image,
  useWindowDimensions,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import tmdbApi from '../services/tmdbApi';
import MediaRow from '../components/MediaRow';
import ContinueWatchingRow from '../components/ContinueWatchingRow';
import RecommendationRow from '../components/RecommendationRow';
import WatchHistoryService from '../services/WatchHistoryService';
import RecommendationService from '../services/RecommendationService';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';

const HomeScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [heroItem, setHeroItem] = useState(null);
  const [popularMovies, setPopularMovies] = useState([]);
  const [topRatedTv, setTopRatedTv] = useState([]);
  const [trending, setTrending] = useState([]);
  const [continueWatching, setContinueWatching] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [recommendationData, setRecommendationData] = useState(null);
  const [recommendationsLoading, setRecommendationsLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // New states for additional categories
  const [appleTvExclusives, setAppleTvExclusives] = useState([]);
  const [netflixOriginals, setNetflixOriginals] = useState([]);
  const [huluOriginals, setHuluOriginals] = useState([]);
  const [amazonOriginals, setAmazonOriginals] = useState([]);
  const [disneyPlusOriginals, setDisneyPlusOriginals] = useState([]);
  const [hboMaxOriginals, setHboMaxOriginals] = useState([]);

  const { width, height } = useWindowDimensions();
  const isLandscape = width > height;

  useEffect(() => {
    loadHomeData(true);
    loadRecommendations();
  }, []);

  const loadRecommendations = async () => {
    try {
      setRecommendationsLoading(true);
      const recData = await RecommendationService.getPersonalizedRecommendations(15);
      setRecommendations(recData.recommendations || []);
      setRecommendationData(recData);
    } catch (error) {
      console.error('Error loading recommendations:', error);
      setRecommendations([]);
      setRecommendationData(null);
    } finally {
      setRecommendationsLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      WatchHistoryService.getContinueWatchingList()
        .then(data => setContinueWatching(data || []))
        .catch(error => console.error('Error loading continue watching on focus:', error));
    }, [])
  );

  const loadHomeData = async (isInitialLoad = false) => {
    if (isInitialLoad) {
      setLoading(true);
    }
    try {
      const [
        trendingData,
        popularMoviesData,
        topRatedTvData,
        continueWatchingData,
        appleTvData,
        netflixData,
        huluData,
        amazonData,
        disneyPlusData,
        hboMaxData,
      ] = await Promise.all([
        tmdbApi.getTrending('all'),
        tmdbApi.getPopularMovies(),
        tmdbApi.getTopRatedTV(),
        WatchHistoryService.getContinueWatchingList(),
        tmdbApi.discoverMediaByNetwork('tv', 2552), // Apple TV+ ID
        tmdbApi.discoverMediaByNetwork('tv', 213),  // Netflix ID. [19]
        tmdbApi.discoverMediaByNetwork('tv', 453),  // Hulu ID
        tmdbApi.discoverMediaByNetwork('tv', 1024), // Amazon ID. [1, 2]
        tmdbApi.discoverMediaByNetwork('tv', 2739), // Disney+ ID
        tmdbApi.discoverMediaByNetwork('tv', 3186), // HBO Max ID
      ]);

      setTrending(trendingData.results || []);
      setPopularMovies(popularMoviesData.results || []);
      setTopRatedTv(topRatedTvData.results || []);
      setContinueWatching(continueWatchingData || []);
      setAppleTvExclusives(appleTvData.results || []);
      setNetflixOriginals(netflixData.results || []);
      setHuluOriginals(huluData.results || []);
      setAmazonOriginals(amazonData.results || []);
      setDisneyPlusOriginals(disneyPlusData.results || []);
      setHboMaxOriginals(hboMaxData.results || []);


      if (trendingData.results && trendingData.results.length > 0) {
        const itemsWithBackdrops = trendingData.results.filter(item => item.backdrop_path);
        
        if (itemsWithBackdrops.length > 0) {
          const randomIndex = Math.floor(Math.random() * itemsWithBackdrops.length);
          setHeroItem(itemsWithBackdrops[randomIndex]);
        } else {
          setHeroItem(trendingData.results.find(item => item.poster_path) || null);
        }
      }
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      if (isInitialLoad) {
        setLoading(false);
      }
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setRefreshKey(prevKey => prevKey + 1);
    await Promise.all([
      loadHomeData(false),
      loadRecommendations()
    ]);
  };

  const handleItemPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('MediaDetail', { item: { ...item, media_type: mediaType }, mediaType });
  };

  const handlePlayPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('Player', { item: { ...item, media_type: mediaType } });
  };

  const handleContinueWatchingPress = (item) => {
    if (item.mediaType === 'tv' && item.season && item.episode) {
      navigation.navigate('Player', {
        item: { ...item, media_type: 'tv' },
        mediaType: 'tv',
        season: item.season,
        episode: item.episode
      });
    } else {
      navigation.navigate('Player', {
        item: { ...item, media_type: 'movie' },
        mediaType: 'movie'
      });
    }
  };

  const handleContinueWatchingDataChange = () => {
    WatchHistoryService.getContinueWatchingList()
      .then(data => setContinueWatching(data || []))
      .catch(error => console.error('Error reloading continue watching:', error));
  };
  
  if (loading) {
    return (
      <View style={slothStyles.loadingContainer}>
        <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
      </View>
    );
  }

  const renderHeroMetadata = () => {
    if (!heroItem) return null;
    const isTvShow = heroItem.media_type === 'tv' || heroItem.first_air_date;
    
    return (
        <View style={slothStyles.heroMetadata}>
           <Text style={slothStyles.metadataText}>{new Date(heroItem.release_date || heroItem.first_air_date).getFullYear()}</Text>
           {isTvShow && heroItem.number_of_seasons && (
              <Text style={slothStyles.metadataText}>{heroItem.number_of_seasons} Season{heroItem.number_of_seasons > 1 ? 's' : ''}</Text>
           )}
           <Text style={slothStyles.badge}>
              {heroItem.adult ? 'TV-MA' : 'PG-13'}
           </Text>
        </View>
    );
  };
  
  return (
    <View style={slothStyles.container}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="light-content"
      />
      <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={SLOTH_COLORS.white}
            />
          }
      >
        {heroItem && (
          <ImageBackground
            source={{ uri: tmdbApi.getBackdropUrl(heroItem.backdrop_path) }}
            style={[
              slothStyles.heroBanner, 
              { height: isLandscape ? width * 0.5625 : height * 0.6 }
            ]}
          >
            <LinearGradient
              colors={['rgba(15, 16, 20, 0.2)', 'rgba(15, 16, 20, 0.7)', SLOTH_COLORS.background]}
              style={slothStyles.heroGradient}
            >
              <SafeAreaView>
                 <View style={slothStyles.header}>
                  <Text style={slothStyles.logo}>S</Text>
                  <View style={slothStyles.headerIcons}>
                    <TouchableOpacity
                      style={[slothStyles.iconContainer, { marginRight: 10 }]}
                      onPress={() => navigation.navigate('MyList')}
                    >
                      <Ionicons name="bookmark-outline" style={slothStyles.icon} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={slothStyles.iconContainer}
                      onPress={() => navigation.navigate('Search')}
                    >
                      <Ionicons name="search-outline" style={slothStyles.icon} />
                    </TouchableOpacity>
                  </View>
                 </View>
              </SafeAreaView>

              <View style={slothStyles.heroContent}>
                <View style={slothStyles.heroOriginalsContainer}></View>

                {/* --- MODIFICATION START --- */}
                {/* We now display the logo image instead of the text title. */}
                <Image
                  source={{
                    uri: `https://sorastream-five.vercel.app/logo/${heroItem.media_type || (heroItem.first_air_date ? 'tv' : 'movie')}/${heroItem.id}`
                  }}
                  style={slothStyles.heroLogo}
                  resizeMode="contain"
                />
                {/* --- MODIFICATION END --- */}
                
                {renderHeroMetadata()}
                <View style={slothStyles.heroButtons}>
                  <TouchableOpacity 
                    style={slothStyles.playButton} 
                    onPress={() => handlePlayPress(heroItem)}
                  >
                    <Ionicons name="play" size={22} color={SLOTH_COLORS.dark} />
                    <Text style={slothStyles.playButtonText}>Play</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={slothStyles.infoButton} 
                    onPress={() => handleItemPress(heroItem)}
                  >
                    <Ionicons name="information-circle-outline" size={22} color={SLOTH_COLORS.white} />
                    <Text style={slothStyles.infoButtonText}>More Info</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </LinearGradient>
          </ImageBackground>
        )}

        {continueWatching.length > 0 && (
          <ContinueWatchingRow
            key={`continue-watching-${refreshKey}`}
            title="Continue Watching"
            data={continueWatching}
            onItemPress={handleContinueWatchingPress}
            onDataChange={handleContinueWatchingDataChange}
          />
        )}
        
        <RecommendationRow
          key={`recommendations-${refreshKey}`}
          title="Recommended for You"
          data={recommendations}
          onItemPress={handleItemPress}
          loading={recommendationsLoading}
          explanation={recommendationData?.explanation}
          onRefresh={loadRecommendations}
          showRefreshButton={true}
        />
        
        <MediaRow
          key={`appletv-${refreshKey}`}
          title="Apple TV+ Exclusives"
          data={appleTvExclusives}
          onItemPress={handleItemPress}
        />

        <MediaRow
            key={`netflix-${refreshKey}`}
            title="Netflix Originals"
            data={netflixOriginals}
            onItemPress={handleItemPress}
        />

        <MediaRow
            key={`hulu-${refreshKey}`}
            title="Hulu Originals"
            data={huluOriginals}
            onItemPress={handleItemPress}
        />

        <MediaRow
            key={`amazon-${refreshKey}`}
            title="Amazon Originals"
            data={amazonOriginals}
            onItemPress={handleItemPress}
        />
        
        <MediaRow
            key={`disneyplus-${refreshKey}`}
            title="Disney+ Originals"
            data={disneyPlusOriginals}
            onItemPress={handleItemPress}
        />

        <MediaRow
            key={`hbomax-${refreshKey}`}
            title="HBO Max Originals"
            data={hboMaxOriginals}
            onItemPress={handleItemPress}
        />

        <MediaRow
            key={`popular-${refreshKey}`}
            title="Popular Media"
            data={popularMovies}
            onItemPress={handleItemPress}
        />
        <MediaRow
            key={`trending-${refreshKey}`}
            title="Trending Now"
            data={trending}
            onItemPress={handleItemPress}
        />
        <MediaRow
            key={`top-rated-${refreshKey}`}
            title="Top Rated TV"
            data={topRatedTv}
            onItemPress={handleItemPress}
        />
      </ScrollView>
    </View>
  );
};

export default HomeScreen;