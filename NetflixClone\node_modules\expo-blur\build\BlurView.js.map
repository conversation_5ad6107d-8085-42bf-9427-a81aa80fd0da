{"version": 3, "file": "BlurView.js", "sourceRoot": "", "sources": ["../src/BlurView.tsx"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,YAAY,CAAC;AAEb,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAC7D,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAIhD,MAAM,cAAc,GAAG,wBAAwB,CAAC,cAAc,CAAC,CAAC;AAEhE,yEAAyE;AACzE,MAAM,CAAC,OAAO,OAAO,QAAS,SAAQ,KAAK,CAAC,SAAwB;IAClE,WAAW,GAAI,KAAK,CAAC,SAAS,EAAyB,CAAC;IAExD;;;;OAIG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC;IACnC,CAAC;IAED,MAAM;QACJ,MAAM,EACJ,IAAI,GAAG,SAAS,EAChB,SAAS,GAAG,EAAE,EACd,mBAAmB,GAAG,CAAC,EACvB,sBAAsB,GAAG,MAAM,EAC/B,KAAK,EACL,QAAQ,EACR,GAAG,KAAK,EACT,GAAG,IAAI,CAAC,KAAK,CAAC;QACf,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAChD;QAAA,CAAC,cAAc,CACb,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CACtB,IAAI,CAAC,CAAC,IAAI,CAAC,CACX,SAAS,CAAC,CAAC,SAAS,CAAC,CACrB,mBAAmB,CAAC,CAAC,mBAAmB,CAAC,CACzC,sBAAsB,CAAC,CAAC,sBAAsB,CAAC,CAC/C,KAAK,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,EAEjC;QAAA,CAAC,QAAQ,CACX;MAAA,EAAE,IAAI,CAAC,CACR,CAAC;IACJ,CAAC;CACF;AAED,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE,EAAE,eAAe,EAAE,aAAa,EAAE;CAC9C,CAAC,CAAC", "sourcesContent": ["// Copyright © 2024 650 Industries.\n\n'use client';\n\nimport { requireNativeViewManager } from 'expo-modules-core';\nimport React from 'react';\nimport { View, StyleSheet } from 'react-native';\n\nimport { BlurViewProps } from './BlurView.types';\n\nconst NativeBlurView = requireNativeViewManager('ExpoBlurView');\n\n// TODO: Class components are not supported with React Server Components.\nexport default class BlurView extends React.Component<BlurViewProps> {\n  blurViewRef? = React.createRef<typeof NativeBlurView>();\n\n  /**\n   * @hidden\n   * When Animated.createAnimatedComponent(BlurView) is used Reanimated will detect and call this\n   * function to determine which component should be animated. We want to animate the NativeBlurView.\n   */\n  getAnimatableRef() {\n    return this.blurViewRef?.current;\n  }\n\n  render() {\n    const {\n      tint = 'default',\n      intensity = 50,\n      blurReductionFactor = 4,\n      experimentalBlurMethod = 'none',\n      style,\n      children,\n      ...props\n    } = this.props;\n    return (\n      <View {...props} style={[styles.container, style]}>\n        <NativeBlurView\n          ref={this.blurViewRef}\n          tint={tint}\n          intensity={intensity}\n          blurReductionFactor={blurReductionFactor}\n          experimentalBlurMethod={experimentalBlurMethod}\n          style={StyleSheet.absoluteFill}\n        />\n        {children}\n      </View>\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  container: { backgroundColor: 'transparent' },\n});\n"]}