import React, { memo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import MediaCard from './MediaCard';

const RecommendationRow = memo(({
  title = "Recommended for You",
  data,
  onItemPress,
  loading = false,
  explanation = null,
  onRefresh = null,
  showRefreshButton = true,
}) => {
  const renderItem = useCallback(({ item }) => {
    return (
      <View style={slothStyles.recommendationCardContainer}>
        <MediaCard
          item={item}
          onPress={onItemPress}
        />
        {/* Explanation text below the card */}
        {item.explanation && (
          <Text style={slothStyles.recommendationExplanation} numberOfLines={2}>
            {item.explanation}
          </Text>
        )}
        {/* Score indicator for debugging (remove in production) */}
        {__DEV__ && item.score && (
          <Text style={slothStyles.recommendationScore}>
            Score: {(item.score * 100).toFixed(0)}%
          </Text>
        )}
      </View>
    );
  }, [onItemPress]);
  
  const keyExtractor = useCallback((item) => `rec_${item.id}_${item.media_type || 'movie'}`, []);

  // Card dimensions for getItemLayout optimization
  const CARD_WIDTH = 140;
  const CARD_MARGIN = 10;
  const EXPLANATION_HEIGHT = 40;
  const CARD_FULL_WIDTH = CARD_WIDTH + CARD_MARGIN;
  const CARD_FULL_HEIGHT = 210 + EXPLANATION_HEIGHT;

  if (loading) {
    return (
      <View style={slothStyles.mediaRowContainer}>
        <View style={slothStyles.recommendationHeader}>
          <Text style={slothStyles.mediaRowTitle}>{title}</Text>
          {showRefreshButton && onRefresh && (
            <TouchableOpacity 
              style={slothStyles.refreshButton}
              onPress={onRefresh}
              disabled={loading}
            >
              <Ionicons name="refresh" size={20} color={SLOTH_COLORS.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
        {explanation && (
          <Text style={slothStyles.recommendationSubtitle}>{explanation}</Text>
        )}
        <View style={{ height: CARD_FULL_HEIGHT, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
          <Text style={slothStyles.loadingText}>Generating recommendations...</Text>
        </View>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return (
      <View style={slothStyles.mediaRowContainer}>
        <View style={slothStyles.recommendationHeader}>
          <Text style={slothStyles.mediaRowTitle}>{title}</Text>
          {showRefreshButton && onRefresh && (
            <TouchableOpacity 
              style={slothStyles.refreshButton}
              onPress={onRefresh}
            >
              <Ionicons name="refresh" size={20} color={SLOTH_COLORS.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
        <View style={slothStyles.emptyRecommendationsContainer}>
          <Ionicons name="bulb-outline" size={48} color={SLOTH_COLORS.textSecondary} />
          <Text style={slothStyles.emptyRecommendationsTitle}>
            No recommendations yet
          </Text>
          <Text style={slothStyles.emptyRecommendationsSubtitle}>
            Watch some content to get personalized recommendations
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={slothStyles.mediaRowContainer}>
      <View style={slothStyles.recommendationHeader}>
        <View style={slothStyles.recommendationTitleContainer}>
          <Text style={slothStyles.mediaRowTitle}>{title}</Text>
          {explanation && (
            <Text style={slothStyles.recommendationSubtitle}>{explanation}</Text>
          )}
        </View>
        {showRefreshButton && onRefresh && (
          <TouchableOpacity 
            style={slothStyles.refreshButton}
            onPress={onRefresh}
          >
            <Ionicons name="refresh" size={20} color={SLOTH_COLORS.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
      
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEnabled={true}
        contentContainerStyle={slothStyles.mediaRowScroll}
        removeClippedSubviews={false}
        maxToRenderPerBatch={7}
        windowSize={5}
        initialNumToRender={5}
        scrollEventThrottle={16}
        gestureResponseDistance={10}
        getItemLayout={(_, index) => ({
          length: CARD_FULL_WIDTH,
          offset: CARD_FULL_WIDTH * index,
          index,
        })}
      />
    </View>
  );
});

export default RecommendationRow;
