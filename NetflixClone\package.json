{"name": "netflixclone", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@gorhom/bottom-sheet": "^5.1.6", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/slider": "^4.5.7", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-av": "~15.1.7", "expo-blur": "^14.1.5", "expo-file-system": "~18.1.11", "expo-linear-gradient": "^14.1.5", "expo-navigation-bar": "~4.2.7", "expo-screen-orientation": "^8.1.7", "expo-status-bar": "~2.2.3", "expo-video": "^2.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-native-system-navigation-bar": "^2.6.4", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.16.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react-native-video": "^5.0.20"}, "private": true}