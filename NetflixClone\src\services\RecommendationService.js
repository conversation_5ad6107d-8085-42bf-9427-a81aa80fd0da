import tmdbApi from './tmdbApi';
import AnalyticsService from './AnalyticsService';
import MyListService from './MyListService';
import WatchHistoryService from './WatchHistoryService';

// Recommendation weights for scoring algorithm
const RECOMMENDATION_WEIGHTS = {
  GENRE_MATCH: 0.25,
  LANGUAGE_MATCH: 0.15,
  RATING_SIMILARITY: 0.20,
  POPULARITY_BOOST: 0.10,
  COMPLETION_RATE: 0.15,
  RECENCY_BOOST: 0.10,
  MY_LIST_PENALTY: -0.05 // Slight penalty for items already in My List
};

// Minimum scores for recommendations
const MIN_RECOMMENDATION_SCORE = 0.3;
const MIN_VOTE_AVERAGE = 6.0;

class RecommendationService {
  constructor() {
    this.userPreferences = null;
    this.genreMap = new Map();
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      // Load user preferences from analytics
      this.userPreferences = await AnalyticsService.getUserPreferences();
      
      // Initialize genre mapping (TMDB genre IDs to names)
      this.genreMap = new Map([
        [28, 'Action'], [12, 'Adventure'], [16, 'Animation'], [35, 'Comedy'],
        [80, 'Crime'], [99, 'Documentary'], [18, 'Drama'], [10751, 'Family'],
        [14, 'Fantasy'], [36, 'History'], [27, 'Horror'], [10402, 'Music'],
        [9648, 'Mystery'], [10749, 'Romance'], [878, 'Science Fiction'],
        [10770, 'TV Movie'], [53, 'Thriller'], [10752, 'War'], [37, 'Western'],
        [10759, 'Action & Adventure'], [10762, 'Kids'], [10763, 'News'],
        [10764, 'Reality'], [10765, 'Sci-Fi & Fantasy'], [10766, 'Soap'],
        [10767, 'Talk'], [10768, 'War & Politics']
      ]);
      
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing recommendation service:', error);
      this.userPreferences = {};
      this.initialized = true;
    }
  }

  async getPersonalizedRecommendations(limit = 20) {
    await this.initialize();
    
    try {
      // Get user data for recommendations
      const [
        analyticsData,
        myListData,
        continueWatching,
        trendingContent
      ] = await Promise.all([
        AnalyticsService.getRecommendationData(),
        MyListService.getAllMyList(),
        WatchHistoryService.getContinueWatchingList(),
        this.getTrendingContent()
      ]);

      // If user has no data, return trending content
      if (!analyticsData.recentViews.length && !myListData.length) {
        return {
          recommendations: trendingContent.slice(0, limit),
          explanation: 'Trending content for new users',
          source: 'trending'
        };
      }

      // Generate recommendations based on user behavior
      const recommendations = await this.generateSmartRecommendations(
        analyticsData,
        myListData,
        continueWatching,
        trendingContent,
        limit
      );

      return recommendations;
    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      // Fallback to trending content
      const trending = await this.getTrendingContent();
      return {
        recommendations: trending.slice(0, limit),
        explanation: 'Trending content',
        source: 'fallback'
      };
    }
  }

  async generateSmartRecommendations(analyticsData, myListData, continueWatching, trendingContent, limit) {
    const { recentViews, recentPlays, completions, preferences } = analyticsData;
    
    // Extract user preferences
    const userGenres = this.extractTopGenres(recentViews, myListData, completions);
    const userLanguages = this.extractTopLanguages(recentViews, myListData);
    const averageRating = this.calculateAverageRating(recentViews, myListData);
    const completionRate = this.calculateCompletionRate(completions);

    // Get content pools for recommendations
    const [similarContent, genreBasedContent] = await Promise.all([
      this.getSimilarContent(recentViews, recentPlays),
      this.getGenreBasedContent(userGenres)
    ]);

    // Combine all potential recommendations
    const allCandidates = [
      ...similarContent,
      ...genreBasedContent,
      ...trendingContent
    ];

    // Remove duplicates and items already in continue watching or my list
    const uniqueCandidates = this.removeDuplicatesAndExisting(
      allCandidates,
      continueWatching,
      myListData
    );

    // Score each candidate
    const scoredRecommendations = uniqueCandidates.map(item => ({
      ...item,
      score: this.calculateRecommendationScore(
        item,
        userGenres,
        userLanguages,
        averageRating,
        completionRate,
        myListData
      ),
      explanation: this.generateExplanation(item, userGenres, recentViews, myListData)
    }));

    // Filter by minimum score and sort
    const filteredRecommendations = scoredRecommendations
      .filter(item => 
        item.score >= MIN_RECOMMENDATION_SCORE && 
        (item.vote_average || 0) >= MIN_VOTE_AVERAGE
      )
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return {
      recommendations: filteredRecommendations,
      explanation: this.generateOverallExplanation(userGenres, recentViews),
      source: 'personalized',
      userPreferences: {
        topGenres: userGenres.slice(0, 3),
        averageRating,
        completionRate
      }
    };
  }

  calculateRecommendationScore(item, userGenres, userLanguages, averageRating, completionRate, myListData) {
    let score = 0;

    // Genre matching score
    const genreScore = this.calculateGenreScore(item, userGenres);
    score += genreScore * RECOMMENDATION_WEIGHTS.GENRE_MATCH;

    // Language matching score
    const languageScore = this.calculateLanguageScore(item, userLanguages);
    score += languageScore * RECOMMENDATION_WEIGHTS.LANGUAGE_MATCH;

    // Rating similarity score
    const ratingScore = this.calculateRatingScore(item, averageRating);
    score += ratingScore * RECOMMENDATION_WEIGHTS.RATING_SIMILARITY;

    // Popularity boost
    const popularityScore = Math.min((item.popularity || 0) / 100, 1);
    score += popularityScore * RECOMMENDATION_WEIGHTS.POPULARITY_BOOST;

    // Completion rate influence
    score += completionRate * RECOMMENDATION_WEIGHTS.COMPLETION_RATE;

    // Recency boost for newer content
    const recencyScore = this.calculateRecencyScore(item);
    score += recencyScore * RECOMMENDATION_WEIGHTS.RECENCY_BOOST;

    // Penalty for items already in My List
    const isInMyList = myListData.some(listItem => 
      listItem.id === item.id && listItem.mediaType === (item.media_type || 'movie')
    );
    if (isInMyList) {
      score += RECOMMENDATION_WEIGHTS.MY_LIST_PENALTY;
    }

    return Math.max(0, Math.min(1, score)); // Clamp between 0 and 1
  }

  calculateGenreScore(item, userGenres) {
    if (!item.genre_ids || !userGenres.length) return 0;
    
    const itemGenres = item.genre_ids;
    const userGenreIds = userGenres.map(g => g.genreId);
    const matches = itemGenres.filter(id => userGenreIds.includes(id));
    
    return matches.length / Math.max(itemGenres.length, userGenreIds.length);
  }

  calculateLanguageScore(item, userLanguages) {
    if (!item.original_language || !userLanguages.length) return 0.5; // Neutral score
    
    const userLang = userLanguages[0]; // Top language preference
    return item.original_language === userLang.language ? 1 : 0.3;
  }

  calculateRatingScore(item, averageRating) {
    if (!item.vote_average || !averageRating) return 0.5;
    
    const difference = Math.abs(item.vote_average - averageRating);
    return Math.max(0, 1 - (difference / 5)); // 5 is max difference on 10-point scale
  }

  calculateRecencyScore(item) {
    const releaseDate = new Date(item.release_date || item.first_air_date || '2000-01-01');
    const now = new Date();
    const ageInYears = (now - releaseDate) / (1000 * 60 * 60 * 24 * 365);
    
    // Boost newer content, but don't penalize classics too much
    if (ageInYears < 1) return 1;
    if (ageInYears < 3) return 0.8;
    if (ageInYears < 5) return 0.6;
    return 0.4;
  }

  extractTopGenres(recentViews, myListData, completions) {
    const genreCount = {};
    
    // Weight completed content more heavily
    [...recentViews, ...myListData, ...completions].forEach(item => {
      const weight = completions.some(c => c.data?.contentId === item.id) ? 2 : 1;
      if (item.genre_ids || item.data?.genres) {
        const genres = item.genre_ids || item.data?.genres || [];
        genres.forEach(genreId => {
          genreCount[genreId] = (genreCount[genreId] || 0) + weight;
        });
      }
    });

    return Object.entries(genreCount)
      .sort(([,a], [,b]) => b - a)
      .map(([genreId, count]) => ({ genreId: parseInt(genreId), count }))
      .slice(0, 5);
  }

  extractTopLanguages(recentViews, myListData) {
    const languageCount = {};
    
    [...recentViews, ...myListData].forEach(item => {
      const language = item.original_language || item.data?.language;
      if (language) {
        languageCount[language] = (languageCount[language] || 0) + 1;
      }
    });

    return Object.entries(languageCount)
      .sort(([,a], [,b]) => b - a)
      .map(([language, count]) => ({ language, count }));
  }

  calculateAverageRating(recentViews, myListData) {
    const ratings = [...recentViews, ...myListData]
      .map(item => item.vote_average || item.data?.rating)
      .filter(rating => rating && rating > 0);
    
    return ratings.length > 0 ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length : 7.0;
  }

  calculateCompletionRate(completions) {
    if (!completions.length) return 0.7; // Default completion rate
    
    const rates = completions
      .map(c => c.data?.completionRate)
      .filter(rate => rate !== undefined);
    
    return rates.length > 0 ? rates.reduce((sum, rate) => sum + rate, 0) / rates.length : 0.7;
  }

  async getSimilarContent(recentViews, recentPlays) {
    const contentIds = [...recentViews, ...recentPlays]
      .map(item => ({ id: item.data?.contentId || item.id, mediaType: item.data?.mediaType || item.mediaType }))
      .slice(0, 5); // Limit to recent 5 items

    const similarContent = [];
    
    for (const { id, mediaType } of contentIds) {
      try {
        const details = mediaType === 'tv' 
          ? await tmdbApi.getTVDetails(id)
          : await tmdbApi.getMovieDetails(id);
        
        if (details.similar?.results) {
          similarContent.push(...details.similar.results.slice(0, 5));
        }
        if (details.recommendations?.results) {
          similarContent.push(...details.recommendations.results.slice(0, 5));
        }
      } catch (error) {
        console.error(`Error getting similar content for ${id}:`, error);
      }
    }

    return similarContent;
  }

  async getGenreBasedContent(userGenres) {
    if (!userGenres.length) return [];
    
    const topGenre = userGenres[0].genreId;
    
    try {
      const [movies, tvShows] = await Promise.all([
        tmdbApi.getMoviesByGenre(topGenre),
        tmdbApi.getTVByGenre(topGenre)
      ]);
      
      return [
        ...(movies.results || []).slice(0, 10),
        ...(tvShows.results || []).slice(0, 10)
      ];
    } catch (error) {
      console.error('Error getting genre-based content:', error);
      return [];
    }
  }

  async getTrendingContent() {
    try {
      const trending = await tmdbApi.getTrending('all');
      return trending.results || [];
    } catch (error) {
      console.error('Error getting trending content:', error);
      return [];
    }
  }

  removeDuplicatesAndExisting(candidates, continueWatching, myListData) {
    const seen = new Set();
    const existingIds = new Set([
      ...continueWatching.map(item => `${item.mediaType}_${item.id}`),
      ...myListData.map(item => `${item.mediaType}_${item.id}`)
    ]);

    return candidates.filter(item => {
      const key = `${item.media_type || 'movie'}_${item.id}`;
      if (seen.has(key) || existingIds.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  generateExplanation(item, userGenres, recentViews, myListData) {
    const itemGenres = item.genre_ids || [];
    const userGenreIds = userGenres.map(g => g.genreId);
    const matchingGenres = itemGenres.filter(id => userGenreIds.includes(id));
    
    if (matchingGenres.length > 0) {
      const genreName = this.genreMap.get(matchingGenres[0]) || 'your favorite genre';
      return `Because you like ${genreName}`;
    }
    
    if (recentViews.length > 0) {
      const recentTitle = recentViews[0].data?.contentTitle || 'recent content';
      return `Because you watched ${recentTitle}`;
    }
    
    return 'Recommended for you';
  }

  generateOverallExplanation(userGenres, recentViews) {
    if (userGenres.length > 0) {
      const topGenre = this.genreMap.get(userGenres[0].genreId) || 'your preferences';
      return `Based on your interest in ${topGenre}`;
    }
    
    if (recentViews.length > 0) {
      return 'Based on your viewing history';
    }
    
    return 'Personalized recommendations';
  }
}

export default new RecommendationService();
